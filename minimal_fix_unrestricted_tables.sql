-- MINIMAL FIX - Just fix the two unrestricted tables
-- This script only fixes the specific unrestricted tables without complex changes

-- =============================================
-- FIX 1: available_cars view
-- =============================================

-- Drop the existing SECURITY DEFINER view
DROP VIEW IF EXISTS public.available_cars CASCADE;

-- Create a simple view without SECURITY DEFINER
CREATE VIEW public.available_cars AS
SELECT 
    c.id,
    c.brand,
    c.model,
    c.year,
    c.daily_rate,
    c.weekly_rate,
    c.monthly_rate,
    c.fuel_type,
    c.transmission,
    c.color,
    c.mileage,
    c.features,
    c.description,
    c.images,
    c.status,
    c.agency_id,
    c.address,
    c.seats,
    c.doors,
    c.license_plate,
    c.created_at,
    c.updated_at,
    a.agency_name,
    a.agency_logo,
    a.location as agency_location,
    a.agency_phone,
    a.rating as agency_rating
FROM public.cars c
INNER JOIN public.agencies a ON c.agency_id = a.id
WHERE c.status = 'available' AND a.is_approved = true;

-- Grant permissions to the view
GRANT SELECT ON public.available_cars TO anon, authenticated;

-- =============================================
-- FIX 2: spatial_ref_sys table (PostGIS system table)
-- =============================================

-- Enable RLS on this table
ALTER TABLE public.spatial_ref_sys ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows everyone to read spatial reference data
DROP POLICY IF EXISTS "allow_read_spatial_ref_sys" ON public.spatial_ref_sys;
CREATE POLICY "allow_read_spatial_ref_sys" ON public.spatial_ref_sys
    FOR SELECT USING (true);

-- Grant permissions
GRANT SELECT ON public.spatial_ref_sys TO anon, authenticated;

-- =============================================
-- VERIFICATION
-- =============================================

-- Check RLS status on the two tables we just fixed
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN 'RESTRICTED' 
        ELSE 'UNRESTRICTED' 
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename = 'spatial_ref_sys'
ORDER BY tablename;

-- Check the view was created
SELECT 
    schemaname, 
    viewname,
    'VIEW CREATED' as status
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'available_cars';

-- Final check - count unrestricted tables
SELECT 
    COUNT(*) as unrestricted_count,
    CASE 
        WHEN COUNT(*) = 0 THEN 'ALL TABLES NOW RESTRICTED'
        ELSE 'SOME TABLES STILL UNRESTRICTED'
    END as final_status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND rowsecurity = false
    AND tablename IN (
        'users', 'agencies', 'cars', 'bookings', 'coupons', 'reviews', 
        'payments', 'gps_tracking', 'notifications', 'messages', 'blogs', 
        'agency_documents', 'spatial_ref_sys'
    );
