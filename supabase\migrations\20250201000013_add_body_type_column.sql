-- Add body_type column to cars table
-- This migration adds the missing body_type column that the application expects

-- =============================================
-- ADD BODY_TYPE COLUMN TO CARS TABLE
-- =============================================

-- Add body_type column with proper constraints
ALTER TABLE public.cars 
ADD COLUMN IF NOT EXISTS body_type TEXT 
CHECK (body_type IN ('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'wagon', 'van', 'truck', 'crossover', 'minivan')) 
DEFAULT 'sedan';

-- Update existing records to have a default body_type based on category if it exists
UPDATE public.cars 
SET body_type = CASE 
    WHEN category = 'suv' THEN 'suv'
    WHEN category = 'van' THEN 'van'
    WHEN category = 'convertible' THEN 'convertible'
    WHEN category = 'sports' THEN 'coupe'
    ELSE 'sedan'
END
WHERE body_type IS NULL;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_cars_body_type ON public.cars(body_type);

-- Add security_deposit column if it doesn't exist (for backward compatibility)
ALTER TABLE public.cars 
ADD COLUMN IF NOT EXISTS security_deposit DECIMAL(10,2) DEFAULT 1000.00;

-- Update existing records to have a default security_deposit
UPDATE public.cars 
SET security_deposit = daily_rate * 2
WHERE security_deposit IS NULL OR security_deposit = 0;
