-- Add ten_days_rate column to cars table
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS ten_days_rate DECIMAL(10,2);

-- Update the available_cars view to include the new column
CREATE OR REPLACE VIEW public.available_cars AS
SELECT 
    c.*,
    a.agency_name,
    a.agency_logo,
    a.rating as agency_rating,
    a.agency_phone,
    a.agency_email,
    a.is_approved as agency_approved
FROM public.cars c
JOIN public.agencies a ON c.agency_id = a.id
WHERE c.status = 'available' 
AND a.is_approved = true;
