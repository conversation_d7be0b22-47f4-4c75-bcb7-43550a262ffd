// Temporarily disable <PERSON><PERSON> to fix admin login
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
})

async function disableRLSTemporarily() {
    console.log('🔧 Temporarily disabling RLS for users table...')
    
    try {
        // Use direct SQL query to disable RLS
        const { data, error } = await supabase
            .rpc('exec_sql', { 
                sql: 'ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;' 
            })
        
        if (error) {
            console.error('❌ Failed to disable RLS:', error.message)
            
            // Try alternative approach - update admin profile directly with service key
            console.log('🔄 Trying alternative approach...')
            
            const { data: adminUser, error: fetchError } = await supabase.auth.admin.listUsers()
            if (fetchError) {
                console.error('❌ Failed to list users:', fetchError.message)
                return false
            }
            
            const admin = adminUser.users.find(u => u.email === '<EMAIL>')
            if (!admin) {
                console.error('❌ Admin user not found')
                return false
            }
            
            // Try to insert/update admin profile using service key
            const { error: upsertError } = await supabase
                .from('users')
                .upsert({
                    id: admin.id,
                    email: '<EMAIL>',
                    first_name: 'Admin',
                    last_name: 'User',
                    role: 'admin',
                    is_verified: true
                }, { 
                    onConflict: 'id',
                    ignoreDuplicates: false 
                })
            
            if (upsertError) {
                console.error('❌ Failed to upsert admin profile:', upsertError.message)
                return false
            }
            
            console.log('✅ Admin profile updated using service key!')
            return true
        }
        
        console.log('✅ RLS disabled successfully!')
        
        // Now update admin profile
        const { data: adminUser, error: fetchError } = await supabase.auth.admin.listUsers()
        if (fetchError) {
            console.error('❌ Failed to list users:', fetchError.message)
            return false
        }
        
        const admin = adminUser.users.find(u => u.email === '<EMAIL>')
        if (!admin) {
            console.error('❌ Admin user not found')
            return false
        }
        
        const { error: updateError } = await supabase
            .from('users')
            .upsert({
                id: admin.id,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                is_verified: true
            })
        
        if (updateError) {
            console.error('❌ Failed to update admin profile:', updateError.message)
            return false
        }
        
        console.log('✅ Admin profile updated!')
        return true
        
    } catch (error) {
        console.error('❌ Unexpected error:', error.message)
        return false
    }
}

disableRLSTemporarily().then(success => {
    if (success) {
        console.log('\n🎉 RLS temporarily disabled and admin profile updated!')
        console.log('You can now test admin login.')
        console.log('Remember to re-enable RLS later for security.')
    } else {
        console.log('\n❌ Failed to disable RLS or update admin profile!')
    }
}).catch(console.error)
