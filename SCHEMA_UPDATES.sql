-- =============================================
-- SCHEMA UPDATES FOR KRIWDRIVE
-- =============================================

-- 1. Add indexes for better performance on car filtering
CREATE INDEX IF NOT EXISTS idx_cars_brand ON public.cars(brand);
CREATE INDEX IF NOT EXISTS idx_cars_color ON public.cars(color);
CREATE INDEX IF NOT EXISTS idx_cars_daily_rate ON public.cars(daily_rate);
CREATE INDEX IF NOT EXISTS idx_cars_year ON public.cars(year);
CREATE INDEX IF NOT EXISTS idx_cars_status ON public.cars(status);
CREATE INDEX IF NOT EXISTS idx_cars_agency_id ON public.cars(agency_id);
CREATE INDEX IF NOT EXISTS idx_cars_location ON public.cars USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_cars_created_at ON public.cars(created_at);

-- 2. Add indexes for agencies
CREATE INDEX IF NOT EXISTS idx_agencies_is_approved ON public.agencies(is_approved);
CREATE INDEX IF NOT EXISTS idx_agencies_rating ON public.agencies(rating);

-- 3. Add indexes for bookings
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_start_date ON public.bookings(start_date);
CREATE INDEX IF NOT EXISTS idx_bookings_end_date ON public.bookings(end_date);

-- 4. Add category field to cars table for better filtering
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS category TEXT CHECK (category IN ('economy', 'compact', 'mid-size', 'full-size', 'premium', 'luxury', 'suv', 'van', 'convertible', 'sports')) DEFAULT 'economy';

-- 5. Add is_featured field for featured listings
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE;

-- 6. Add view count for popularity tracking
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;

-- 7. Update the cars table to ensure all required fields for filtering
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS city TEXT;
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS availability_start DATE;
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS availability_end DATE;
ALTER TABLE public.cars ADD COLUMN IF NOT EXISTS ten_days_rate DECIMAL(10,2);

-- 8. Create a function to update car availability based on bookings
CREATE OR REPLACE FUNCTION update_car_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- Update car status based on active bookings
    IF NEW.status = 'confirmed' OR NEW.status = 'active' THEN
        UPDATE public.cars 
        SET status = 'rented' 
        WHERE id = NEW.car_id;
    ELSIF OLD.status IN ('confirmed', 'active') AND NEW.status IN ('completed', 'cancelled') THEN
        -- Check if there are other active bookings for this car
        IF NOT EXISTS (
            SELECT 1 FROM public.bookings 
            WHERE car_id = NEW.car_id 
            AND status IN ('confirmed', 'active')
            AND id != NEW.id
        ) THEN
            UPDATE public.cars 
            SET status = 'available' 
            WHERE id = NEW.car_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Create trigger for automatic car availability updates
DROP TRIGGER IF EXISTS trigger_update_car_availability ON public.bookings;
CREATE TRIGGER trigger_update_car_availability
    AFTER UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_car_availability();

-- 10. Create a view for available cars with agency info
CREATE OR REPLACE VIEW public.available_cars AS
SELECT 
    c.*,
    a.agency_name,
    a.agency_logo,
    a.rating as agency_rating,
    a.agency_phone,
    a.agency_email,
    a.is_approved as agency_approved
FROM public.cars c
JOIN public.agencies a ON c.agency_id = a.id
WHERE c.status = 'available' 
AND a.is_approved = true;

-- 11. Create function to increment view count
CREATE OR REPLACE FUNCTION increment_car_view_count(car_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.cars 
    SET view_count = view_count + 1 
    WHERE id = car_id;
END;
$$ LANGUAGE plpgsql;

-- 12. Add RLS policies for the new view
ALTER VIEW public.available_cars OWNER TO postgres;

-- 13. Create function for advanced car search
CREATE OR REPLACE FUNCTION search_cars(
    search_term TEXT DEFAULT NULL,
    min_price DECIMAL DEFAULT NULL,
    max_price DECIMAL DEFAULT NULL,
    car_brand TEXT DEFAULT NULL,
    car_color TEXT DEFAULT NULL,
    car_category TEXT DEFAULT NULL,
    car_city TEXT DEFAULT NULL,
    min_year INTEGER DEFAULT NULL,
    max_year INTEGER DEFAULT NULL,
    fuel_type_filter TEXT DEFAULT NULL,
    transmission_filter TEXT DEFAULT NULL,
    min_seats INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    brand TEXT,
    model TEXT,
    year INTEGER,
    color TEXT,
    daily_rate DECIMAL,
    images TEXT[],
    agency_name TEXT,
    agency_rating DECIMAL,
    city TEXT,
    category TEXT,
    fuel_type TEXT,
    transmission TEXT,
    seats INTEGER,
    view_count INTEGER,
    is_featured BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.brand,
        c.model,
        c.year,
        c.color,
        c.daily_rate,
        c.images,
        a.agency_name,
        a.rating,
        c.city,
        c.category,
        c.fuel_type,
        c.transmission,
        c.seats,
        c.view_count,
        c.is_featured
    FROM public.cars c
    JOIN public.agencies a ON c.agency_id = a.id
    WHERE c.status = 'available'
    AND a.is_approved = true
    AND (search_term IS NULL OR 
         c.brand ILIKE '%' || search_term || '%' OR
         c.model ILIKE '%' || search_term || '%' OR
         c.city ILIKE '%' || search_term || '%' OR
         a.agency_name ILIKE '%' || search_term || '%')
    AND (min_price IS NULL OR c.daily_rate >= min_price)
    AND (max_price IS NULL OR c.daily_rate <= max_price)
    AND (car_brand IS NULL OR c.brand = car_brand)
    AND (car_color IS NULL OR c.color = car_color)
    AND (car_category IS NULL OR c.category = car_category)
    AND (car_city IS NULL OR c.city = car_city)
    AND (min_year IS NULL OR c.year >= min_year)
    AND (max_year IS NULL OR c.year <= max_year)
    AND (fuel_type_filter IS NULL OR c.fuel_type = fuel_type_filter)
    AND (transmission_filter IS NULL OR c.transmission = transmission_filter)
    AND (min_seats IS NULL OR c.seats >= min_seats)
    ORDER BY c.is_featured DESC, c.view_count DESC, c.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 14. Update admin email in sample data (if exists)
UPDATE public.users 
SET email = '<EMAIL>' 
WHERE role = 'admin' AND email LIKE '%modrivet%' OR email LIKE '%morentcar%';

-- 15. Create notification for new car listings
CREATE OR REPLACE FUNCTION notify_new_car_listing()
RETURNS TRIGGER AS $$
BEGIN
    -- Notify admin about new car listing
    INSERT INTO public.notifications (user_id, title, message, type, data)
    SELECT 
        u.id,
        'New Car Listed',
        'A new car has been added: ' || NEW.brand || ' ' || NEW.model,
        'system',
        jsonb_build_object('car_id', NEW.id, 'agency_id', NEW.agency_id)
    FROM public.users u
    WHERE u.role = 'admin';
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 16. Create trigger for new car notifications
DROP TRIGGER IF EXISTS trigger_notify_new_car ON public.cars;
CREATE TRIGGER trigger_notify_new_car
    AFTER INSERT ON public.cars
    FOR EACH ROW
    EXECUTE FUNCTION notify_new_car_listing();

-- 17. Grant necessary permissions
GRANT SELECT ON public.available_cars TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_cars TO anon, authenticated;
GRANT EXECUTE ON FUNCTION increment_car_view_count TO anon, authenticated;

-- =============================================
-- INSTRUCTIONS FOR APPLYING THESE UPDATES
-- =============================================

-- 1. Run this SQL script in your Supabase SQL Editor
-- 2. Verify all indexes were created successfully
-- 3. Test the search_cars function with sample data
-- 4. Update any existing car records to include the new fields:
--    - category (if not already set)
--    - city (if not already set)
--    - is_featured (defaults to false)
--    - view_count (defaults to 0)

-- Example update for existing cars:
-- UPDATE public.cars SET
--   category = 'economy'
-- WHERE category IS NULL;

-- UPDATE public.cars SET
--   city = 'Marrakech'
-- WHERE city IS NULL AND location ILIKE '%marrakech%';

-- 5. Test the car filtering functionality in the listings page
-- 6. Verify that new car listings trigger notifications
-- 7. Check that car availability updates automatically with bookings
