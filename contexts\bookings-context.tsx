'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase/client'
import { useAuth } from './auth-context'

export interface Booking {
    id: string
    carId: string
    userId?: string
    agencyId?: string
    customerName: string
    startDate: string
    endDate: string
    status: 'pending' | 'accepted' | 'rejected'
    images?: string[]
    totalAmount?: number
    deposit?: number // Add deposit property
    pickupLocation?: string
    dropoffLocation?: string
    notes?: string
}

interface BookingsContextType {
    bookings: Booking[]
    loading: boolean
    error: string | null
    addBooking: (booking: Omit<Booking, 'id'>) => Promise<void>
    deleteBooking: (id: string) => Promise<void>
    updateBooking: (id: string, updates: Partial<Booking>) => Promise<void>
}

const BookingsContext = createContext<BookingsContextType | undefined>(undefined)

export const BookingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { user } = useAuth()
    const [bookings, setBookings] = useState<Booking[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Fetch bookings from Supabase on mount and when user changes
    useEffect(() => {
        if (!user) {
            setBookings([])
            setLoading(false)
            return
        }
        setLoading(true)
        setError(null)
        supabase
            .from('bookings')
            .select('*')
            .eq('user_id', user.id)
            .then(({ data, error }) => {
                if (error) {
                    setError(error.message)
                    setBookings([])
                } else {
                    setBookings(data || [])
                }
                setLoading(false)
            })
    }, [user])

    // Add booking to Supabase
    const addBooking = async (booking: Omit<Booking, 'id'>) => {
        if (!user) return
        setLoading(true)
        setError(null)
        const { data, error } = await supabase
            .from('bookings')
            .insert({ ...booking, user_id: user.id })
            .select()
            .single()
        if (error) {
            setError(error.message)
        } else if (data) {
            setBookings(prev => [...prev, data])
        }
        setLoading(false)
    }

    // Delete booking from Supabase
    const deleteBooking = async (id: string) => {
        setLoading(true)
        setError(null)
        const { error } = await supabase
            .from('bookings')
            .delete()
            .eq('id', id)
        if (error) {
            setError(error.message)
        } else {
            setBookings(prev => prev.filter(b => b.id !== id))
        }
        setLoading(false)
    }

    // Update booking in Supabase
    const updateBooking = async (id: string, updates: Partial<Booking>) => {
        setLoading(true)
        setError(null)
        const { data, error } = await supabase
            .from('bookings')
            .update(updates)
            .eq('id', id)
            .select()
            .single()
        if (error) {
            setError(error.message)
        } else if (data) {
            setBookings(prev => prev.map(b => b.id === id ? { ...b, ...data } : b))
        }
        setLoading(false)
    }

    return (
        <BookingsContext.Provider value={{ bookings, loading, error, addBooking, deleteBooking, updateBooking }}>
            {children}
        </BookingsContext.Provider>
    )
}

export function useBookings() {
    const ctx = useContext(BookingsContext)
    if (!ctx) throw new Error('useBookings must be used within a BookingsProvider')
    return ctx
} 