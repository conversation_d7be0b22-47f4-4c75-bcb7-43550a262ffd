-- Fix RLS policies to prevent infinite recursion
-- Run this in your Supabase SQL editor

-- First, disable <PERSON><PERSON> temporarily to fix the policies
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on users table
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Create simple, non-recursive policies
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create admin policy using email instead of role lookup to avoid recursion
CREATE POLICY "Admin can view all users" ON public.users
    FOR SELECT USING (
        auth.jwt() ->> 'email' = '<EMAIL>'
    );

CREATE POLICY "Admin can update all users" ON public.users
    FOR UPDATE USING (
        auth.jwt() ->> 'email' = '<EMAIL>'
    );

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Fix agencies table policies too
DROP POLICY IF EXISTS "Admins can manage all agencies" ON public.agencies;

CREATE POLICY "Admin can manage all agencies" ON public.agencies
    FOR ALL USING (
        auth.jwt() ->> 'email' = '<EMAIL>'
    );

-- Fix other tables that might have admin policies
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;
CREATE POLICY "Admin can manage all cars" ON public.cars
    FOR ALL USING (
        auth.jwt() ->> 'email' = '<EMAIL>'
    );

DROP POLICY IF EXISTS "Admins can manage all bookings" ON public.bookings;
CREATE POLICY "Admin can manage all bookings" ON public.bookings
    FOR ALL USING (
        auth.jwt() ->> 'email' = '<EMAIL>'
    );

-- Ensure admin user has correct role
UPDATE public.users 
SET role = 'admin', is_verified = true
WHERE email = '<EMAIL>';

-- Create admin profile if it doesn't exist
INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
SELECT 
    au.id,
    au.email,
    'Admin',
    'User',
    'admin',
    true
FROM auth.users au
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.users pu WHERE pu.id = au.id
);
