"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Carousel from "@/components/ui/carousel"
import { FeaturedCarCard } from "@/components/features/car/featured-car-card"
import { HowItWorks } from "@/components/shared/how-it-works"
import { useI18n } from "@/i18n/i18n-provider"
import { useRouter } from "next/navigation"
import { Search, MapPin, Car, Calendar, Star, TrendingUp, Users, Shield, Zap, ArrowRight, Play, ChevronRight } from "lucide-react"
import { CAR_BRANDS } from "@/utils/constants"
import { supabase } from "@/lib/supabase/client"
import Link from "next/link"

const SearchIcon = Search

export default function Home() {
  const { t } = useI18n()
  const router = useRouter()
  const [location, setLocation] = useState("")
  const [carType, setCarType] = useState("")
  const [showCityDropdown, setShowCityDropdown] = useState(false)
  const [blogs, setBlogs] = useState<any[]>([])
  const [loadingBlogs, setLoadingBlogs] = useState(true)

  const moroccanCities = [
    "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berkane", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla", "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune", "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi", "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
  ]

  const carBrandsAZ = [
    "All Brands", ...CAR_BRANDS
  ]

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const searchParams = new URLSearchParams()
    if (location) searchParams.set("location", location)
    if (carType && carType !== "All Brands") searchParams.set("brand", carType)
    router.push(`/listings?${searchParams.toString()}`)
  }

  const stats = [
    { icon: Car, value: "500+", label: "Cars Available", color: "text-blue-600" },
    { icon: Users, value: "10K+", label: "Happy Customers", color: "text-green-600" },
    { icon: MapPin, value: "50+", label: "Cities Covered", color: "text-purple-600" },
    { icon: Star, value: "4.8", label: "Average Rating", color: "text-orange-600" }
  ]

  // Fetch recent blogs
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const { data, error } = await supabase
          .from('blogs')
          .select('*')
          .eq('status', 'published')
          .order('created_at', { ascending: false })
          .limit(3)

        if (error) {
          console.error('Error fetching blogs:', error)
        } else {
          setBlogs(data || [])
        }
      } catch (error) {
        console.error('Error fetching blogs:', error)
      } finally {
        setLoadingBlogs(false)
      }
    }

    fetchBlogs()
  }, [])

  return (
    <>
      {/* Hero Section with Enhanced Design */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background image with zoom effect */}
        <img
          src="/images/image9.png"
          alt="Discover Morocco"
          className="absolute inset-0 w-full h-300 object-cover animate-slow-zoom"
        />
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/40"></div>

        <style jsx>{`
          @keyframes slow-zoom {
            0% {
              transform: scale(1);
            }
            100% {
              transform: scale(1.1);
            }
          }

          .animate-slow-zoom {
            animation: slow-zoom 20s ease-in-out infinite alternate;
          }
        `}</style>

        <div className="container relative z-10 px-4 md:px-6">
          <div className="flex flex-col items-center text-center space-y-8">
            <div className="space-y-6 max-w-4xl">
              <div className="space-y-4">
                <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm font-medium">
                  <Shield className="w-4 h-4" />
                  Trusted by 10,000+ customers across Morocco
                </div>
                <h1 className="text-4xl md:text-6xl font-extrabold tracking-tighter bg-gradient-to-r from-white via-gray-100 to-gray-200 bg-clip-text text-transparent drop-shadow-lg">
                  Discover Morocco on wheels
                </h1>
                <p className="mx-auto max-w-[700px] text-white/90 md:text-2xl font-semibold drop-shadow-lg leading-relaxed">
                  Rent from trusted agencies across Morocco
                </p>
              </div>
            </div>

            {/* Enhanced Search Form */}
            <div className="w-full max-w-4xl bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 border border-white/20">
              <form onSubmit={handleSearch} className="flex flex-col lg:flex-row w-full items-center gap-4">
                {/* Location Input */}
                <div className="flex-1 w-full">
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      className="w-full pl-10 pr-4 py-3 text-lg border-2 border-gray-200 focus:border-primary transition-all duration-300 rounded-xl"
                      placeholder="Select a city"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      onFocus={() => setShowCityDropdown(true)}
                      onBlur={() => setTimeout(() => setShowCityDropdown(false), 150)}
                      autoComplete="off"
                    />
                    {showCityDropdown && (
                      <ul className="absolute left-0 right-0 top-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto z-20 text-black">
                        {moroccanCities.filter(city => city.toLowerCase().includes(location.toLowerCase())).map(city => (
                          <li
                            key={city}
                            className="px-4 py-3 hover:bg-primary/10 cursor-pointer text-base transition-colors duration-200"
                            onMouseDown={() => { setLocation(city); setShowCityDropdown(false); }}
                          >
                            {city}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>

                {/* Car Make Input */}
                <div className="flex-1 w-full">
                  <div className="relative">
                    <Car className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Select value={carType} onValueChange={setCarType}>
                      <SelectTrigger className="w-full pl-10 pr-4 py-3 text-lg border-2 border-gray-200 focus:border-primary transition-all duration-300 rounded-xl">
                        <SelectValue placeholder="Car Make" />
                      </SelectTrigger>
                      <SelectContent>
                        {carBrandsAZ.map(brand => (
                          <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Search Button */}
                <Button
                  type="submit"
                  className="w-full lg:w-auto px-8 py-3 text-lg font-semibold bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <SearchIcon className="mr-2 h-5 w-5" />
                  Search Cars
                </Button>
              </form>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronRight className="w-6 h-6 text-white rotate-90" />
        </div>
      </section>

      <HowItWorks />

      {/* Begin Your Journey Section */}
      <section
        className="w-full py-8 md:py-16 lg:py-28 relative flex items-center justify-center"
        style={{ minHeight: '350px' }}
      >
        <img
          src="/images/image2.png"
          alt="Begin your journey background"
          className="absolute inset-0 w-full h-full object-cover rounded-3xl shadow-lg"
          style={{ zIndex: 0 }}
        />
        <div className="absolute inset-0 bg-black/30 rounded-3xl" style={{ zIndex: 1 }} />
        <div className="relative z-10 flex flex-col items-center justify-center w-full h-full px-2 md:px-6">
          <div className="rounded-2xl p-6 md:p-8 max-w-md text-white shadow-xl w-full flex flex-col gap-4 items-center"
            style={{ backgroundColor: '#F36B0B' }}>
            <h2 className="text-2xl md:text-3xl font-bold leading-tight text-center">Begin your journey</h2>
            <p className="text-sm md:text-base opacity-90 text-center">Browse our collection of vehicles and start your adventure today. Find the perfect car for your next trip with just a few clicks.</p>
            <button
              className="mt-2 bg-white text-[#020817] font-semibold rounded-full px-4 py-2 shadow hover:bg-orange-100 transition w-fit"
              onClick={() => router.push('/listings')}
            >
              See Listings
            </button>
          </div>
        </div>
      </section>

      {/* Enhanced Featured Cars Section */}
      <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-12">
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl font-bold tracking-tighter bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                {t("home.featuredCars.title")}
              </h2>
              <p className="max-w-[900px] text-gray-600 md:text-xl leading-relaxed">
                {t("home.featuredCars.subtitle")}
              </p>
            </div>
            <div className="flex justify-center">
              <Button
                variant="outline"
                size="lg"
                onClick={() => router.push('/listings')}
                className="rounded-full px-8 py-3 text-lg border-2 hover:border-primary hover:bg-primary hover:text-white transition-all duration-300"
              >
                {t("home.featuredCars.viewAll")}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
          <Carousel className="py-8">
            <FeaturedCarCard
              id="car1"
              title="Tesla Model 3"
              price={75}
              location="Marrakech"
              images={["/placeholder.svg?height=400&width=600&text=Tesla+Front", "/placeholder.svg?height=400&width=600&text=Tesla+Side", "/placeholder.svg?height=400&width=600&text=Tesla+Interior"]}
              rating={4.9}
              reviews={234}
              category="Electric"
            />
            <FeaturedCarCard
              id="car2"
              title="BMW 3 Series"
              price={85}
              location="Casablanca"
              images={["/placeholder.svg?height=400&width=600&text=BMW+Front", "/placeholder.svg?height=400&width=600&text=BMW+Side", "/placeholder.svg?height=400&width=600&text=BMW+Interior"]}
              rating={4.8}
              reviews={189}
              category="Luxury"
            />
            <FeaturedCarCard
              id="car3"
              title="Toyota RAV4"
              price={60}
              location="Rabat"
              images={["/placeholder.svg?height=400&width=600&text=RAV4+Front", "/placeholder.svg?height=400&width=600&text=RAV4+Side", "/placeholder.svg?height=400&width=600&text=RAV4+Interior"]}
              rating={4.7}
              reviews={312}
              category="SUV"
            />
            <FeaturedCarCard
              id="car4"
              title="Honda Civic"
              price={45}
              location="Fes"
              images={["/placeholder.svg?height=400&width=600&text=Civic+Front", "/placeholder.svg?height=400&width=600&text=Civic+Side", "/placeholder.svg?height=400&width=600&text=Civic+Interior"]}
              rating={4.6}
              reviews={178}
              category="Economy"
            />
          </Carousel>
        </div>
      </section>

      {/* Enhanced Become Our Partner Section */}
      <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-r from-primary via-secondary to-accent text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="grid gap-8 lg:grid-cols-2 lg:gap-12 xl:grid-cols-2 items-center">
            <div className="flex flex-col justify-center space-y-6">
              <div className="space-y-4">
                <h2 className="text-4xl md:text-5xl font-bold tracking-tighter">
                  Become Our Partner
                </h2>
                <p className="max-w-[600px] text-white/90 md:text-xl leading-relaxed">
                  {t("home.becomeHost.subtitle")}
                </p>
              </div>
              <div className="flex flex-col gap-4 min-[400px]:flex-row">
                <Button
                  className="bg-white text-primary hover:bg-gray-100 font-semibold rounded-full px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  onClick={() => router.push('/for-agencies')}
                >
                  <Zap className="mr-2 h-5 w-5" />
                  {t("home.becomeHost.learnMore")}
                </Button>
                <Button
                  variant="outline"
                  className="border-white text-black hover:bg-white/10 font-semibold rounded-full px-8 py-4 text-lg backdrop-blur-sm transition-all duration-300"
                  onClick={() => router.push('/auth?tab=signup&role=agency')}
                >
                  {t("home.becomeHost.register")}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-2xl transform rotate-3 group-hover:rotate-0 transition-transform duration-500"></div>
                <img
                  alt="Become Our Partner"
                  className="rounded-2xl object-cover shadow-2xl transform -rotate-1 group-hover:rotate-0 transition-transform duration-500"
                  height={400}
                  src="/images/partner.png"
                  style={{ aspectRatio: "500/400", objectFit: "cover" }}
                  width={500}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section className="w-full py-16 md:py-24 lg:py-32 bg-white">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-12">
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl font-bold tracking-tighter bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Latest Travel Tips & Stories
              </h2>
              <p className="max-w-[900px] text-gray-600 md:text-xl leading-relaxed">
                Discover the best travel destinations, tips, and stories from Morocco
              </p>
            </div>
          </div>

          {loadingBlogs ? (
            <div className="flex justify-center items-center py-12">
              <div className="w-8 h-8 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
              <span className="ml-2">Loading blogs...</span>
            </div>
          ) : blogs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {blogs.map((blog) => (
                <Card key={blog.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative h-48">
                    <img
                      src={blog.image || "/placeholder.svg"}
                      alt={blog.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl font-bold line-clamp-2">{blog.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground line-clamp-3 mb-4">
                      {blog.content?.slice(0, 150)}...
                    </p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(blog.created_at).toLocaleDateString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600">No blogs available at the moment.</p>
            </div>
          )}

          <div className="flex justify-center">
            <Button
              variant="outline"
              size="lg"
              onClick={() => router.push('/blogs')}
              className="rounded-full px-8 py-3 text-lg border-2 hover:border-primary hover:bg-primary hover:text-white transition-all duration-300"
            >
              View More Blogs
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>
    </>
  )
}
