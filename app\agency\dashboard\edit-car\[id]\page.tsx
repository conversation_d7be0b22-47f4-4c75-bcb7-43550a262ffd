"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Upload, X, Shield, DollarSign } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import { AuthGuard } from "@/components/features/auth/auth-guard"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Image from 'next/image'
import { supabase } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/auth-context'

import { CAR_BRANDS, CAR_COLORS } from '@/utils/constants';

// Define proper car interface
interface CarType {
  id?: string;
  brand: string; // Use 'brand' instead of 'make'
  model: string;
  year: string;
  licensePlate: string;
  seats: string;
  doors: string;
  color: string;
  mileage: string;
  security_deposit: string; // Use 'security_deposit' instead of 'deposit'
  address: string; // Use 'address' instead of 'location'
  pricePerDay: string;
  pricePerWeek: string;
  pricePerTenDays: string;
  pricePerMonth: string;
  description: string;
  fuel_type: string;
  transmission: string;
  body_type: string;
  insuranceType?: string; // Add missing insurance type
}

const moroccanCities = [
  "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla",
  "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune",
  "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi",
  "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
].sort((a, b) => a.localeCompare(b))

// Sample car data - this will be replaced with actual data fetching
const carData = {
  id: "CAR-AGY-001-001",
  make: "Toyota",
  model: "Corolla",
  year: 2022,
  transmission: "automatic",
  price: 750,
  pricePerDay: 750,
  pricePerWeek: 4500,
  pricePerMonth: 15000,
  mileage: 15000,
  insuranceType: "comprehensive",
  deposit: 5000,
  color: "Red",
  location: "Agadir",
  images: [
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
  ],
  description: "Premium sedan with excellent fuel efficiency and modern features.",
  available: true,
}

const availableFeatures = [
  { key: 'ac', name: 'AC', icon: '/icons/car-air-conditioning-svgrepo-com.svg', type: 'boolean' },
  { key: 'bluetooth', name: 'Bluetooth', icon: '/icons/bluetooth-on-svgrepo-com.svg', type: 'boolean' },
  { key: 'airbags', name: 'Airbags', icon: '/icons/car-seat-with-seatbelt-svgrepo-com.svg', type: 'boolean' },
  { key: 'gps', name: 'GPS', icon: '/icons/gps.svg', type: 'boolean' },
  { key: 'usbCharger', name: 'USB Charger', icon: '/icons/usb.svg', type: 'boolean' },
  { key: 'sunroof', name: 'Sunroof', icon: '/icons/sunroof.svg', type: 'boolean' },
];

const bodyTypeOptions = ['SUV', 'Sedan', 'Coupe', 'Convertible', 'Hatchback', 'Wagon', 'Van', 'Truck'];
const fuelTypeOptions = ['gasoline', 'diesel', 'hybrid', 'electric', 'plug-in_hybrid', 'hydrogen'];

export default function EditCarPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const carId = params.id as string
  const [car, setCar] = useState<CarType | null>(null)
  const [carImages, setCarImages] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [features, setFeatures] = useState<Record<string, string | boolean>>({
    transmission: '',
    year: '',
    ac: true,
    engineCapacity: '',
    bluetooth: true,
    airbags: true,
    gps: false,
    usbCharger: true,
    sunroof: false,
  })

  // Mock function removed - using real database query in useEffect

  useEffect(() => {
    if (carId) {
      setIsLoading(true)
      supabase.from('cars').select('*').eq('id', carId).single().then(({ data, error }) => {
        if (error) {
          toast.error("Car not found")
          router.push("/agency/dashboard?tab=cars")
        } else {
          // Set car data with proper field mapping
          setCar({
            id: data.id,
            brand: data.brand, // Use brand instead of make
            model: data.model,
            year: data.year.toString(),
            licensePlate: data.license_plate,
            seats: data.seats.toString(),
            doors: data.doors.toString(),
            color: data.color,
            mileage: data.mileage.toString(),
            security_deposit: data.security_deposit.toString(), // Use security_deposit instead of deposit
            address: data.address, // Use address instead of location
            pricePerDay: data.daily_rate.toString(),
            pricePerWeek: data.weekly_rate?.toString() || '',
            pricePerTenDays: data.ten_days_rate?.toString() || '',
            pricePerMonth: data.monthly_rate?.toString() || '',
            description: data.description,
            fuel_type: data.fuel_type,
            transmission: data.transmission,
            body_type: data.body_type,
            insuranceType: data.insurance_info?.type || 'basic'
          })
          setCarImages(data.images || [])

          // Set features based on database data
          const carFeatures = data.features || []
          const featureMap = {
            air_conditioning: 'ac',
            bluetooth: 'bluetooth',
            airbags: 'airbags',
            gps: 'gps',
            usb_charging: 'usbCharger',
            sunroof: 'sunroof'
          }

          const mappedFeatures: Record<string, any> = {}
          carFeatures.forEach((feature: string) => {
            const mappedKey = featureMap[feature as keyof typeof featureMap] || feature
            mappedFeatures[mappedKey] = true
          })

          // Add body type and fuel type to features for form handling
          if (data.body_type) mappedFeatures.bodyType = data.body_type
          if (data.fuel_type) mappedFeatures.fuelType = data.fuel_type
          if (data.transmission) mappedFeatures.transmission = data.transmission

          setFeatures(mappedFeatures)
        }
        setIsLoading(false)
      })
    }
  }, [carId, router])

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files).map((file) => URL.createObjectURL(file))
      setCarImages([...carImages, ...newImages])
    }
  }

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...carImages]
    updatedImages.splice(index, 1)
    setCarImages(updatedImages)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Map features to database format
      const mappedFeatures = Object.keys(features).filter(key => features[key]).map(key => {
        switch (key) {
          case 'ac': return 'air_conditioning'
          case 'bluetooth': return 'bluetooth'
          case 'airbags': return 'airbags'
          case 'gps': return 'gps'
          case 'usbCharger': return 'usb_charging'
          case 'sunroof': return 'sunroof'
          default: return key
        }
      })

      // Gather updated car data from form state
      if (!car) {
        toast.error("Car data not loaded")
        return
      }

      const updatedCar = {
        brand: car.brand, // Use brand instead of make
        model: car.model,
        year: parseInt(car.year),
        license_plate: car.licensePlate,
        seats: parseInt(car.seats),
        doors: parseInt(car.doors),
        color: car.color,
        mileage: parseInt(car.mileage),
        security_deposit: parseFloat(car.security_deposit), // Use security_deposit instead of deposit
        address: car.address, // Use address instead of location
        daily_rate: parseFloat(car.pricePerDay),
        weekly_rate: car.pricePerWeek ? parseFloat(car.pricePerWeek) : null,
        ten_days_rate: car.pricePerTenDays ? parseFloat(car.pricePerTenDays) : null,
        monthly_rate: car.pricePerMonth ? parseFloat(car.pricePerMonth) : null,
        fuel_type: features.fuelType && typeof features.fuelType === 'string' ? features.fuelType.toLowerCase() : car.fuel_type,
        transmission: features.transmission || car.transmission,
        body_type: features.bodyType || car.body_type,
        description: car.description,
        insurance_info: { type: car.insuranceType || 'basic' },
        features: mappedFeatures,
        images: carImages,
      }

      const { error } = await supabase.from('cars').update(updatedCar).eq('id', carId)
      if (error) {
        console.error('Update error:', error)
        toast.error(`Failed to update car: ${error.message}`)
      } else {
        toast.success("Car updated successfully")
        router.push("/agency/dashboard?tab=cars")
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <AuthGuard requiredRole="agency">
      <div className="container py-10">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Edit Car</h1>
          <Button variant="outline" size="sm" asChild>
            <Link href="/agency/dashboard?tab=cars">Back to Dashboard</Link>
          </Button>
        </div>

        {isLoading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading car details...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader>
                <CardTitle>Car Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="brand">Brand</Label>
                    <Select
                      value={car?.brand || ''}
                      onValueChange={(value) => car && setCar({ ...car, brand: value })}
                      required
                    >
                      <SelectTrigger id="brand">
                        <SelectValue placeholder="Select a brand" />
                      </SelectTrigger>
                      <SelectContent>
                        {CAR_BRANDS.map(brand => (
                          <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="model">Model</Label>
                    <Input
                      id="model"
                      placeholder="e.g. Corolla"
                      value={car?.model || ''}
                      onChange={(e) => car && setCar({ ...car, model: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="color">Color</Label>
                    <div className="flex gap-2 flex-wrap">
                      {["White", "Black", "Silver", "Gray", "Red", "Blue", "Green", "Yellow", "Orange", "Purple"].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded border-2 flex items-center justify-center focus:outline-none ${car?.color === color ? 'border-blue-600 ring-2 ring-blue-300' : 'border-gray-300'}`}
                          style={{ backgroundColor: typeof color === 'string' ? color.toLowerCase() : color }}
                          aria-label={color}
                          onClick={() => car && setCar({ ...car, color })}
                        >
                          {car?.color === color && <span className="w-3 h-3 rounded-full bg-white border border-blue-600" />}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="year">Year</Label>
                    <Select
                      value={car?.year || ''}
                      onValueChange={(value) => car && setCar({ ...car, year: value })}
                      required
                    >
                      <SelectTrigger id="year">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 30 }, (_, i) => `${2024 - i}`).map(year => (
                          <SelectItem key={year} value={year}>{year}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="licensePlate">License Plate</Label>
                    <Input
                      id="licensePlate"
                      placeholder="e.g. 123456-A-12"
                      required
                      value={car?.licensePlate || ''}
                      onChange={e => car && setCar({ ...car, licensePlate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="seats">Number of Seats</Label>
                    <Select
                      required
                      value={car?.seats || ''}
                      onValueChange={(value) => car && setCar({ ...car, seats: value })}
                    >
                      <SelectTrigger id="seats">
                        <SelectValue placeholder="Select seats" />
                      </SelectTrigger>
                      <SelectContent>
                        {[2, 4, 5, 7, 8, 9].map(num => (
                          <SelectItem key={num} value={num.toString()}>{num} seats</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="doors">Number of Doors</Label>
                    <Select
                      required
                      value={car?.doors || ''}
                      onValueChange={(value) => car && setCar({ ...car, doors: value })}
                    >
                      <SelectTrigger id="doors">
                        <SelectValue placeholder="Select doors" />
                      </SelectTrigger>
                      <SelectContent>
                        {[2, 3, 4, 5].map(num => (
                          <SelectItem key={num} value={num.toString()}>{num} doors</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>



                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="mileage">Mileage (km)</Label>
                    <Input
                      id="mileage"
                      type="number"
                      placeholder="e.g. 15000"
                      value={car?.mileage || ''}
                      onChange={(e) => car && setCar({ ...car, mileage: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="security_deposit">Security Deposit (MAD)</Label>
                    <Input
                      id="security_deposit"
                      type="number"
                      placeholder="e.g. 5000"
                      value={car?.security_deposit || ''}
                      onChange={(e) => car && setCar({ ...car, security_deposit: e.target.value })}
                      required
                    />
                    <p className="text-sm text-muted-foreground">Amount required as security deposit from the renter</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="address">Location</Label>
                    <Select
                      value={car?.address || ''}
                      onValueChange={(value) => car && setCar({ ...car, address: value })}
                      required
                    >
                      <SelectTrigger id="address">
                        <SelectValue placeholder="Select a location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map(city => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Pricing Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Pricing Options</h3>
                  </div>

                  <Card>
                    <CardContent className="pt-6">
                      <Tabs defaultValue="daily" className="w-full">
                        <TabsList className="grid grid-cols-4 mb-4">
                          <TabsTrigger value="daily">Daily Rate</TabsTrigger>
                          <TabsTrigger value="weekly">Weekly Rate</TabsTrigger>
                          <TabsTrigger value="ten-days">10 Days Rate</TabsTrigger>
                          <TabsTrigger value="monthly">Monthly Rate</TabsTrigger>
                        </TabsList>

                        <TabsContent value="daily">
                          <div className="space-y-2">
                            <Label htmlFor="daily-price">Daily Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="daily-price"
                                type="number"
                                placeholder="e.g. 750"
                                className="pl-10"
                                value={car?.pricePerDay || ''}
                                onChange={(e) => car && setCar({ ...car, pricePerDay: e.target.value })}
                                required
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Standard daily rental rate</p>
                          </div>
                        </TabsContent>

                        <TabsContent value="weekly">
                          <div className="space-y-2">
                            <Label htmlFor="weekly-price">Weekly Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="weekly-price"
                                type="number"
                                placeholder="e.g. 4500"
                                className="pl-10"
                                value={car?.pricePerWeek || ''}
                                onChange={(e) => car && setCar({ ...car, pricePerWeek: e.target.value })}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Discounted rate for 7+ day rentals</p>
                          </div>
                        </TabsContent>

                        <TabsContent value="ten-days">
                          <div className="space-y-2">
                            <Label htmlFor="ten-days-price">10 Days Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="ten-days-price"
                                type="number"
                                placeholder="e.g. 6500"
                                className="pl-10"
                                value={car?.pricePerTenDays || ''}
                                onChange={(e) => car && setCar({ ...car, pricePerTenDays: e.target.value })}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Discounted rate for 10 day rentals</p>
                          </div>
                        </TabsContent>

                        <TabsContent value="monthly">
                          <div className="space-y-2">
                            <Label htmlFor="monthly-price">Monthly Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="monthly-price"
                                type="number"
                                placeholder="e.g. 15000"
                                className="pl-10"
                                value={car?.pricePerMonth || ''}
                                onChange={(e) => car && setCar({ ...car, pricePerMonth: e.target.value })}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Discounted rate for 30+ day rentals</p>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </div>

                {/* Car Features Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Car Features</h3>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    {availableFeatures.slice(0, 8).map((feature) => (
                      <div key={feature.key} className="flex items-center gap-4 mb-2">
                        <Image src={feature.icon} alt={feature.name} width={32} height={32} />
                        <span className="w-32">{feature.name}</span>
                        {feature.type === 'boolean' && (
                          <input type="checkbox" checked={!!features[feature.key]} onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.checked }))} />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Insurance Options */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-primary" />
                    <Label className="text-base font-medium">Insurance Type</Label>
                  </div>

                  <RadioGroup
                    value={car?.insuranceType || 'basic'}
                    onValueChange={(value) => car && setCar({ ...car, insuranceType: value })}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4"
                  >
                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="basic" id="basic" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="basic" className="font-medium">
                          Basic Insurance
                        </Label>
                        <p className="text-sm text-muted-foreground">Covers third-party liability only</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="comprehensive" id="comprehensive" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="comprehensive" className="font-medium">
                          Comprehensive
                        </Label>
                        <p className="text-sm text-muted-foreground">Covers damage to the vehicle and third-party</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="premium" id="premium" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="premium" className="font-medium">
                          Premium
                        </Label>
                        <p className="text-sm text-muted-foreground">Full coverage with zero deductible</p>
                      </div>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your car..."
                    className="min-h-[120px]"
                    value={car?.description || ''}
                    onChange={(e) => car && setCar({ ...car, description: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Car Images</Label>
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">Current Images ({carImages.length})</p>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {carImages.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`Car image ${index + 1}`}
                            className="w-full h-24 object-cover rounded-md"
                          />
                          <button
                            type="button"
                            className="absolute top-1 right-1 bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemoveImage(index)}
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center mt-4">
                    <Upload className="h-10 w-10 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground mb-1">Add more images (multiple allowed)</p>
                    <p className="text-xs text-muted-foreground mb-4">PNG, JPG (max. 5MB per image)</p>
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => document.getElementById("image-upload")?.click()}
                    >
                      Select Images
                    </Button>
                    <Input
                      id="image-upload"
                      type="file"
                      className="hidden"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button" asChild>
                  <Link href="/agency/dashboard?tab=cars">Cancel</Link>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Saving Changes..." : "Save Changes"}
                </Button>
              </CardFooter>
            </Card>
          </form>
        )}
      </div>
    </AuthGuard>
  )
}
