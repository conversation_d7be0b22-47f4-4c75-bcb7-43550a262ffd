-- Fix booking constraint to allow guest bookings without email
-- This migration removes the requirement for guest_email to be NOT NULL

-- Drop the existing constraint
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS booking_user_or_guest;

-- Add the new constraint that allows guest bookings without email
ALTER TABLE public.bookings ADD CONSTRAINT booking_user_or_guest CHECK (
    (user_id IS NOT NULL AND is_guest_booking = FALSE) OR
    (user_id IS NULL AND is_guest_booking = TRUE AND guest_name IS NOT NULL)
);

-- Update any existing bookings that might have issues
UPDATE public.bookings 
SET guest_email = NULL 
WHERE is_guest_booking = TRUE AND guest_email = '';
