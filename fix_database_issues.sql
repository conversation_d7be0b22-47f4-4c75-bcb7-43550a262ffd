-- EMERGENCY DATABASE FIX SCRIPT
-- Run this script in your Supabase SQL Editor to fix all issues

-- =============================================
-- STEP 1: FIX USER ROLES CONSTRAINT ISSUE
-- =============================================

-- First, update existing users with 'user' role to 'inactive'
UPDATE public.users SET role = 'inactive' WHERE role = 'user';

-- Drop and recreate the constraint to allow 'inactive' users
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE public.users ADD CONSTRAINT users_role_check CHECK (role IN ('agency', 'admin', 'inactive'));

-- =============================================
-- STEP 2: FIX BOOKING CONSTRAINT
-- =============================================

-- Drop the existing constraint that requires guest_email
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS booking_user_or_guest;

-- Add new constraint that allows guest bookings without email
ALTER TABLE public.bookings ADD CONSTRAINT booking_user_or_guest CHECK (
    (user_id IS NOT NULL AND is_guest_booking = FALSE) OR
    (user_id IS NULL AND is_guest_booking = TRUE AND guest_name IS NOT NULL)
);

-- =============================================
-- STEP 3: ENABLE RLS ON ALL TABLES
-- =============================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gps_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_documents ENABLE ROW LEVEL SECURITY;

-- =============================================
-- STEP 4: DROP ALL EXISTING POLICIES
-- =============================================

-- Users policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Agencies can view own profile" ON public.users;
DROP POLICY IF EXISTS "Agencies can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Agencies policies
DROP POLICY IF EXISTS "Agencies are viewable by all" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update their agency" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can insert their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can register as agencies" ON public.agencies;
DROP POLICY IF EXISTS "Admins can manage all agencies" ON public.agencies;
DROP POLICY IF EXISTS "Anyone can view agencies" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can create agencies" ON public.agencies;

-- Cars policies
DROP POLICY IF EXISTS "Cars are viewable by all" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can manage their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Users can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Guest bookings can be created" ON public.bookings;
DROP POLICY IF EXISTS "Anyone can create guest bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can update their agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Admins can manage all bookings" ON public.bookings;
DROP POLICY IF EXISTS "Enable read access for all bookings" ON public.bookings;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.bookings;

-- =============================================
-- STEP 5: CREATE ESSENTIAL POLICIES
-- =============================================

-- Users table policies
CREATE POLICY "Agencies can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id AND role IN ('agency', 'admin'));

CREATE POLICY "Agencies can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id AND role IN ('agency', 'admin'));

CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Agencies table policies
CREATE POLICY "Anyone can view approved agencies" ON public.agencies
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Admins can view all agencies" ON public.agencies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Agency owners can view own agency" ON public.agencies
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Agency owners can update own agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Cars table policies
CREATE POLICY "Anyone can view available cars from approved agencies" ON public.cars
    FOR SELECT USING (
        status = 'available' AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE id = cars.agency_id AND is_approved = true
        )
    );

CREATE POLICY "Agency owners can manage their cars" ON public.cars
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Bookings table policies
CREATE POLICY "Anyone can create guest bookings" ON public.bookings
    FOR INSERT WITH CHECK (is_guest_booking = true AND user_id IS NULL);

CREATE POLICY "Agency owners can view their agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Agency owners can manage their agency bookings" ON public.bookings
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Coupons table policies
CREATE POLICY "Agency owners can manage their coupons" ON public.coupons
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can view active coupons for validation" ON public.coupons
    FOR SELECT USING (is_active = true);

-- Basic policies for other tables
CREATE POLICY "Anyone can view reviews" ON public.reviews FOR SELECT USING (true);
CREATE POLICY "Anyone can create reviews" ON public.reviews FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can create messages" ON public.messages FOR INSERT WITH CHECK (true);
CREATE POLICY "Admins can view all messages" ON public.messages FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin')
);

CREATE POLICY "Anyone can view published blogs" ON public.blogs FOR SELECT USING (status = 'published');
CREATE POLICY "Admins can manage all blogs" ON public.blogs FOR ALL USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin')
);

-- =============================================
-- STEP 6: GRANT PERMISSIONS TO ANON USERS
-- =============================================

GRANT SELECT ON public.agencies TO anon;
GRANT SELECT ON public.cars TO anon;
GRANT SELECT ON public.reviews TO anon;
GRANT SELECT ON public.blogs TO anon;
GRANT SELECT ON public.coupons TO anon;
GRANT INSERT ON public.bookings TO anon;
GRANT INSERT ON public.messages TO anon;
GRANT INSERT ON public.reviews TO anon;

-- =============================================
-- STEP 7: CREATE HELPER FUNCTIONS
-- =============================================

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Check if all tables have RLS enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;
