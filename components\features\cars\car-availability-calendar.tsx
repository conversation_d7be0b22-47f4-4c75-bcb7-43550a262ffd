"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase } from '@/lib/supabase/client'
import ReactCalendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css'

interface Booking {
  id: string
  start_date: string
  end_date: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled'
}

interface Car {
  id: string
  availability_start: string | null
  availability_end: string | null
}

interface CarAvailabilityCalendarProps {
  carId: string
  className?: string
}

export function CarAvailabilityCalendar({ carId, className }: CarAvailabilityCalendarProps) {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [bookedDates, setBookedDates] = useState<Date[]>([])
  const [car, setCar] = useState<Car | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [carId])

  const fetchData = async () => {
    try {
      setIsLoading(true)

      // Fetch car details including availability dates
      const { data: carData, error: carError } = await supabase
        .from('cars')
        .select('id, availability_start, availability_end')
        .eq('id', carId)
        .single()

      if (carError) {
        console.error('Error fetching car:', carError)
      } else {
        setCar(carData)
      }

      // Fetch bookings - only show accepted/confirmed bookings as unavailable
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('id, start_date, end_date, status')
        .eq('car_id', carId)
        .in('status', ['confirmed', 'active', 'accepted'])

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError)
        return
      }

      setBookings(bookingsData || [])

      // Generate booked dates from bookings
      const dates: Date[] = []
      bookingsData?.forEach(booking => {
        const startDate = new Date(booking.start_date)
        const endDate = new Date(booking.end_date)

        // Add all dates between start and end (inclusive)
        const currentDate = new Date(startDate)
        while (currentDate <= endDate) {
          dates.push(new Date(currentDate))
          currentDate.setDate(currentDate.getDate() + 1)
        }
      })

      setBookedDates(dates)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const tileClassName = ({ date }: { date: Date }) => {
    const isBooked = bookedDates.some(bookedDate =>
      bookedDate.toDateString() === date.toDateString()
    )

    // Check if date is outside availability window - only show when there are accepted bookings
    const hasAcceptedBookings = bookings.some(booking =>
      booking.status === 'confirmed' || booking.status === 'active' || booking.status === 'accepted'
    )

    const isOutsideAvailability = car && hasAcceptedBookings && (
      (car.availability_start && date < new Date(car.availability_start)) ||
      (car.availability_end && date > new Date(car.availability_end))
    )

    if (isBooked) {
      return 'rc-booked'
    }

    if (isOutsideAvailability) {
      return 'rc-unavailable'
    }

    return 'rc-available'
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Availability Calendar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Availability Calendar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <ReactCalendar
            tileClassName={tileClassName}
            className="w-full text-base"
            prev2Label={null}
            next2Label={null}
            locale="en-US"
            minDate={new Date()}
          />
        </div>
        <div className="flex gap-4 mt-4 text-xs">
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 bg-red-300 rounded"></span>
            <span>Booked</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 bg-gray-400 rounded"></span>
            <span>Unavailable</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 border border-blue-500 rounded"></span>
            <span>Today</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 bg-gray-100 rounded"></span>
            <span>Available</span>
          </div>
        </div>

        {/* Booking summary */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-2">Availability Summary</h4>
          <div className="text-xs text-gray-600">
            <p>Total bookings: {bookings.length}</p>
            <p>Booked days: {bookedDates.length}</p>
            {car?.availability_start && (
              <p>Available from: {new Date(car.availability_start).toLocaleDateString()}</p>
            )}
            {car?.availability_end && (
              <p>Available until: {new Date(car.availability_end).toLocaleDateString()}</p>
            )}
          </div>
        </div>

        <style jsx global>{`
          .rc-booked {
            background: #fecaca !important;
            color: #b91c1c !important;
            font-weight: bold;
            border-radius: 6px;
          }
          .rc-unavailable {
            background: #9ca3af !important;
            color: #374151 !important;
            border-radius: 6px;
            opacity: 0.6;
          }
          .rc-available {
            background: #f3f4f6;
            border-radius: 6px;
          }
          .react-calendar__tile:enabled:hover,
          .react-calendar__tile:enabled:focus {
            background-color: #e5e7eb;
          }
          .react-calendar__tile--now {
            background: #3b82f6 !important;
            color: white !important;
            border-radius: 6px;
          }
          .react-calendar__tile--active {
            background: #1d4ed8 !important;
            color: white !important;
          }
        `}</style>
      </CardContent>
    </Card>
  )
}
