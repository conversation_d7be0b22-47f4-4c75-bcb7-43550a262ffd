// Script to run SQL fixes using service role key
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
    console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
})

async function runSQLFixes() {
    console.log('🔧 Running SQL fixes for RLS policies...')
    
    try {
        // First, let's check if admin user exists in auth.users
        console.log('1. Checking admin user in auth.users...')
        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()
        
        if (authError) {
            console.error('❌ Failed to list auth users:', authError.message)
            return false
        }
        
        const adminUser = authUsers.users.find(u => u.email === '<EMAIL>')
        if (!adminUser) {
            console.error('❌ Admin user not found in auth.users')
            return false
        }
        
        console.log('✅ Admin user found:', adminUser.id)
        
        // Disable RLS temporarily
        console.log('2. Disabling RLS on users table...')
        await supabase.rpc('exec_sql', { 
            sql: 'ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;' 
        })
        
        // Drop existing policies
        console.log('3. Dropping existing policies...')
        const dropPolicies = [
            'DROP POLICY IF EXISTS "Users can view own profile" ON public.users;',
            'DROP POLICY IF EXISTS "Users can update own profile" ON public.users;',
            'DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;',
            'DROP POLICY IF EXISTS "Admins can view all users" ON public.users;',
            'DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;'
        ]
        
        for (const sql of dropPolicies) {
            await supabase.rpc('exec_sql', { sql })
        }
        
        // Create new policies
        console.log('4. Creating new policies...')
        const newPolicies = [
            'CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);',
            'CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);',
            'CREATE POLICY "Users can insert own profile" ON public.users FOR INSERT WITH CHECK (auth.uid() = id);',
            'CREATE POLICY "Admin can view all users" ON public.users FOR SELECT USING (auth.jwt() ->> \'email\' = \'<EMAIL>\');',
            'CREATE POLICY "Admin can update all users" ON public.users FOR UPDATE USING (auth.jwt() ->> \'email\' = \'<EMAIL>\');'
        ]
        
        for (const sql of newPolicies) {
            await supabase.rpc('exec_sql', { sql })
        }
        
        // Re-enable RLS
        console.log('5. Re-enabling RLS...')
        await supabase.rpc('exec_sql', { 
            sql: 'ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;' 
        })
        
        // Update admin user profile
        console.log('6. Updating admin user profile...')
        const { error: updateError } = await supabase
            .from('users')
            .upsert({
                id: adminUser.id,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                is_verified: true
            })
        
        if (updateError) {
            console.error('❌ Failed to update admin profile:', updateError.message)
            return false
        }
        
        console.log('✅ Admin profile updated successfully!')
        return true
        
    } catch (error) {
        console.error('❌ SQL fix failed:', error.message)
        return false
    }
}

runSQLFixes().then(success => {
    if (success) {
        console.log('\n🎉 SQL fixes completed successfully!')
        console.log('Admin login should now work properly.')
    } else {
        console.log('\n❌ SQL fixes failed!')
    }
}).catch(console.error)
