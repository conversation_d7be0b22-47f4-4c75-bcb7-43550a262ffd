-- FIXED VERSION - Quick fix for remaining issues
-- This script specifically targets the two unrestricted tables and key security issues

-- =============================================
-- STEP 1: FIX THE TWO UNRESTRICTED TABLES
-- =============================================

-- Fix 1: available_cars view
-- The issue is that it's a SECURITY DEFINER view, let's recreate it properly
DROP VIEW IF EXISTS public.available_cars CASCADE;

-- Create a new view without SECURITY DEFINER
CREATE VIEW public.available_cars AS
SELECT
    c.id,
    c.brand,
    c.model,
    c.year,
    c.daily_rate,
    c.weekly_rate,
    c.monthly_rate,
    c.fuel_type,
    c.transmission,
    c.color,
    c.mileage,
    c.features,
    c.description,
    c.images,
    c.status,
    c.agency_id,
    c.address,
    c.seats,
    c.doors,
    c.license_plate,
    c.created_at,
    c.updated_at,
    a.agency_name,
    a.agency_logo,
    a.location as agency_location,
    a.agency_phone,
    a.rating as agency_rating
FROM public.cars c
INNER JOIN public.agencies a ON c.agency_id = a.id
WHERE c.status = 'available' AND a.is_approved = true;

-- Grant permissions to the view
GRANT SELECT ON public.available_cars TO anon, authenticated;

-- Fix 2: spatial_ref_sys table (PostGIS system table)
-- Enable RLS on this table
ALTER TABLE public.spatial_ref_sys ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows everyone to read spatial reference data
DROP POLICY IF EXISTS "allow_read_spatial_ref_sys" ON public.spatial_ref_sys;
CREATE POLICY "allow_read_spatial_ref_sys" ON public.spatial_ref_sys
    FOR SELECT USING (true);

-- Grant permissions
GRANT SELECT ON public.spatial_ref_sys TO anon, authenticated;

-- =============================================
-- STEP 2: FIX CRITICAL SECURITY ISSUES
-- =============================================

-- Fix the is_admin function to be more secure
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$;

-- Fix other critical functions
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- =============================================
-- STEP 3: CLEAN UP DUPLICATE POLICIES (PERFORMANCE FIX)
-- =============================================

-- Remove duplicate policies on agencies table
DROP POLICY IF EXISTS "anyone_can_view_approved_agencies" ON public.agencies;
DROP POLICY IF EXISTS "agency_owners_can_view_own_agency" ON public.agencies;
DROP POLICY IF EXISTS "admins_can_view_all_agencies" ON public.agencies;

-- Create single optimized SELECT policy for agencies
CREATE POLICY "agencies_select_optimized" ON public.agencies
    FOR SELECT USING (
        is_approved = true OR 
        user_id = (SELECT auth.uid()) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

-- Remove duplicate policies on bookings table
DROP POLICY IF EXISTS "agency_owners_can_view_their_bookings" ON public.bookings;
DROP POLICY IF EXISTS "agency_owners_can_manage_their_bookings" ON public.bookings;
DROP POLICY IF EXISTS "admins_can_manage_all_bookings" ON public.bookings;

-- Create single optimized policies for bookings
CREATE POLICY "bookings_select_optimized" ON public.bookings
    FOR SELECT USING (
        agency_id IN (SELECT id FROM public.agencies WHERE user_id = (SELECT auth.uid())) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

CREATE POLICY "bookings_modify_optimized" ON public.bookings
    FOR ALL USING (
        (is_guest_booking = true AND user_id IS NULL) OR
        agency_id IN (SELECT id FROM public.agencies WHERE user_id = (SELECT auth.uid())) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

-- =============================================
-- STEP 4: ADD MISSING POLICIES FOR TABLES THAT NEED THEM
-- =============================================

-- Add policies for tables that might be missing them
DROP POLICY IF EXISTS "gps_tracking_access" ON public.gps_tracking;
CREATE POLICY "gps_tracking_access" ON public.gps_tracking
    FOR ALL USING (
        car_id IN (
            SELECT c.id FROM public.cars c
            INNER JOIN public.agencies a ON c.agency_id = a.id
            WHERE a.user_id = (SELECT auth.uid())
        ) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

DROP POLICY IF EXISTS "payments_access" ON public.payments;
CREATE POLICY "payments_access" ON public.payments
    FOR ALL USING (
        booking_id IN (
            SELECT b.id FROM public.bookings b
            INNER JOIN public.agencies a ON b.agency_id = a.id
            WHERE a.user_id = (SELECT auth.uid())
        ) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

DROP POLICY IF EXISTS "notifications_access" ON public.notifications;
CREATE POLICY "notifications_access" ON public.notifications
    FOR ALL USING (
        user_id = (SELECT auth.uid()) OR
        EXISTS (SELECT 1 FROM public.users WHERE id = (SELECT auth.uid()) AND role = 'admin')
    );

-- =============================================
-- STEP 5: VERIFICATION QUERIES
-- =============================================

-- Check RLS status on all important tables
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN 'RESTRICTED' 
        ELSE 'UNRESTRICTED' 
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'users', 'agencies', 'cars', 'bookings', 'coupons', 'reviews', 
        'payments', 'gps_tracking', 'notifications', 'messages', 'blogs', 
        'agency_documents', 'spatial_ref_sys'
    )
ORDER BY tablename;

-- Check views
SELECT 
    schemaname, 
    viewname,
    'VIEW' as type
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'available_cars';

-- Count policies per table
SELECT 
    schemaname, 
    tablename, 
    COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public' 
GROUP BY schemaname, tablename
ORDER BY tablename;

-- Final verification
SELECT 
    'Database Security Status' as check_type,
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM pg_tables 
            WHERE schemaname = 'public' 
                AND tablename IN (
                    'users', 'agencies', 'cars', 'bookings', 'coupons', 'reviews', 
                    'payments', 'gps_tracking', 'notifications', 'messages', 'blogs', 
                    'agency_documents', 'spatial_ref_sys'
                )
                AND rowsecurity = false
        ) = 0 THEN 'ALL TABLES SECURED'
        ELSE 'SOME TABLES STILL UNRESTRICTED'
    END as status;
