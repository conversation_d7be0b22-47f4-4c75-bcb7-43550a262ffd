-- Fix RLS policies for all tables
-- This migration re-enables RLS and creates proper policies for agency-only authentication

-- =============================================
-- ENABLE RLS ON ALL TABLES
-- =============================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gps_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_documents ENABLE ROW LEVEL SECURITY;

-- =============================================
-- DROP ALL EXISTING POLICIES
-- =============================================

-- Users policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Agencies can view own profile" ON public.users;
DROP POLICY IF EXISTS "Agencies can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Agencies policies
DROP POLICY IF EXISTS "Agencies are viewable by all" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update their agency" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can insert their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can register as agencies" ON public.agencies;
DROP POLICY IF EXISTS "Admins can manage all agencies" ON public.agencies;

-- Cars policies
DROP POLICY IF EXISTS "Cars are viewable by all" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can manage their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Users can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Guest bookings can be created" ON public.bookings;
DROP POLICY IF EXISTS "Anyone can create guest bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can update their agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Admins can manage all bookings" ON public.bookings;

-- =============================================
-- CREATE NEW POLICIES FOR AGENCY-ONLY SYSTEM
-- =============================================

-- Users table policies (only agencies and admins)
CREATE POLICY "Agencies can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id AND role IN ('agency', 'admin'));

CREATE POLICY "Agencies can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id AND role IN ('agency', 'admin'));

CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Agencies table policies
CREATE POLICY "Anyone can view approved agencies" ON public.agencies
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Admins can view all agencies" ON public.agencies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Agency owners can view own agency" ON public.agencies
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Agency owners can update own agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Cars table policies
CREATE POLICY "Anyone can view available cars from approved agencies" ON public.cars
    FOR SELECT USING (
        status = 'available' AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE id = cars.agency_id AND is_approved = true
        )
    );

CREATE POLICY "Agency owners can manage their cars" ON public.cars
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Bookings table policies
CREATE POLICY "Anyone can create guest bookings" ON public.bookings
    FOR INSERT WITH CHECK (is_guest_booking = true AND user_id IS NULL);

CREATE POLICY "Agency owners can view their agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Agency owners can manage their agency bookings" ON public.bookings
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Coupons table policies
CREATE POLICY "Agency owners can manage their coupons" ON public.coupons
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can view active coupons for validation" ON public.coupons
    FOR SELECT USING (is_active = true);

-- Reviews table policies
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT USING (true);

CREATE POLICY "Anyone can create reviews" ON public.reviews
    FOR INSERT WITH CHECK (true);

-- Messages table policies (for contact forms)
CREATE POLICY "Anyone can create messages" ON public.messages
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all messages" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Blogs table policies
CREATE POLICY "Anyone can view published blogs" ON public.blogs
    FOR SELECT USING (status = 'published');

CREATE POLICY "Admins can manage all blogs" ON public.blogs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Agency documents policies
CREATE POLICY "Agency owners can manage their documents" ON public.agency_documents
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can view public agency documents" ON public.agency_documents
    FOR SELECT USING (is_public = true);

CREATE POLICY "Admins can manage all agency documents" ON public.agency_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- GRANT PERMISSIONS TO ANON USERS FOR PUBLIC ACCESS
-- =============================================

-- Allow anonymous users to view public data
GRANT SELECT ON public.agencies TO anon;
GRANT SELECT ON public.cars TO anon;
GRANT SELECT ON public.reviews TO anon;
GRANT SELECT ON public.blogs TO anon;
GRANT SELECT ON public.agency_documents TO anon;
GRANT SELECT ON public.coupons TO anon;

-- Allow anonymous users to create bookings and messages
GRANT INSERT ON public.bookings TO anon;
GRANT INSERT ON public.messages TO anon;
GRANT INSERT ON public.reviews TO anon;

-- =============================================
-- CREATE HELPER FUNCTIONS
-- =============================================

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns agency
CREATE OR REPLACE FUNCTION public.is_agency_owner(agency_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.agencies
        WHERE id = agency_id AND user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
