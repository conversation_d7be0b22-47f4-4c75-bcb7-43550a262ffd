// Shared car types
import type { Agency } from './auth';
import type { Booking } from './booking';

export interface Car {
    id: string;
    agencyId: string;
    brand: string;
    model: string;
    year: number;
    licensePlate: string;
    color: string;
    fuelType: FuelType;
    transmission: Transmission;
    seats: number;
    doors: number;
    dailyRate: number;
    weeklyRate?: number;
    tenDaysRate?: number;
    monthlyRate?: number;
    status: CarStatus;
    description?: string;
    features: CarFeature[];
    images: string[];
    location: {
        latitude: number;
        longitude: number;
        address: string;
    };
    mileage: number;
    insuranceInfo?: InsuranceInfo;
    createdAt: string;
    updatedAt: string;

    // Relations
    agency?: Agency;
    bookings?: Booking[];
    gpsData?: GPSData;
}

export interface CarCreateData {
    brand: string;
    model: string;
    year: number;
    licensePlate: string;
    color: string;
    fuelType: FuelType;
    transmission: Transmission;
    seats: number;
    doors: number;
    dailyRate: number;
    weeklyRate?: number;
    tenDaysRate?: number;
    monthlyRate?: number;
    description?: string;
    features: CarFeature[];
    images: File[];
    location: {
        latitude: number;
        longitude: number;
        address: string;
    };
    mileage: number;
    insuranceInfo?: InsuranceInfo;
}

export interface CarUpdateData extends Partial<CarCreateData> {
    status?: CarStatus;
}

export type FuelType =
    | 'gasoline'
    | 'diesel'
    | 'electric'
    | 'hybrid'
    | 'plug-in_hybrid'
    | 'hydrogen';

export type Transmission =
    | 'manual'
    | 'automatic'
    | 'semi_automatic';

export type CarStatus =
    | 'available'
    | 'rented'
    | 'maintenance'
    | 'unavailable'
    | 'reserved';

export type CarFeature =
    | 'air_conditioning'
    | 'bluetooth'
    | 'gps'
    | 'backup_camera'
    | 'parking_sensors'
    | 'cruise_control'
    | 'heated_seats'
    | 'sunroof'
    | 'usb_charging'
    | 'wifi'
    | 'child_seat'
    | 'roof_rack'
    | 'bike_rack'
    | 'pet_friendly'
    | 'smoking_allowed'
    | 'wheelchair_accessible';

export interface InsuranceInfo {
    provider: string;
    policyNumber: string;
    expiryDate: string;
    coverage: string[];
}

export interface CarFilters {
    brand?: string;
    model?: string;
    fuelType?: FuelType;
    transmission?: Transmission;
    seats?: number;
    minPrice?: number;
    maxPrice?: number;
    features?: CarFeature[];
    location?: {
        latitude: number;
        longitude: number;
        radius: number;
    };
    startDate?: string;
    endDate?: string;
    agencyId?: string;
    status?: CarStatus;
    page?: number;
    limit?: number;
}

export interface CarStats {
    total: number;
    available: number;
    rented: number;
    maintenance: number;
    revenue: number;
    averageRating: number;
}

export interface GPSData {
    carId: string;
    latitude: number;
    longitude: number;
    speed?: number;
    heading?: number;
    timestamp: string;
    batteryLevel?: number;
    isOnline: boolean;
} 