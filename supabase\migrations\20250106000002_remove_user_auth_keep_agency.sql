-- Remove user authentication and keep only agency authentication
-- This migration removes user-specific features and keeps only agency functionality

-- 1. First, update existing users with 'user' role to 'inactive' before adding constraint
UPDATE public.users SET role = 'inactive' WHERE role = 'user';

-- 2. Now update users table role constraint to only allow 'agency', 'admin', and 'inactive'
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE public.users ADD CONSTRAINT users_role_check CHECK (role IN ('agency', 'admin', 'inactive'));

-- 3. Remove user-specific policies and keep only agency and admin policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;

-- Create new policies for agencies and admins only
CREATE POLICY "Agencies can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id AND role = 'agency');

CREATE POLICY "Agencies can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id AND role = 'agency');

CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 4. Update booking policies to remove user-specific policies
DROP POLICY IF EXISTS "Users can view own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Users can create bookings" ON public.bookings;

-- Keep only guest booking and agency policies
CREATE POLICY "Anyone can create guest bookings" ON public.bookings
    FOR INSERT WITH CHECK (is_guest_booking = true AND user_id IS NULL);

-- 5. Add a comment to track this change
COMMENT ON TABLE public.users IS 'Users table now only supports agency and admin roles. Regular user authentication has been removed.';

-- 6. Create a view for active agencies only
CREATE OR REPLACE VIEW public.active_agencies AS
SELECT
    a.*,
    u.email,
    u.first_name,
    u.last_name,
    u.phone
FROM public.agencies a
JOIN public.users u ON a.user_id = u.id
WHERE u.role = 'agency' AND a.is_approved = true;

-- Grant access to the view
GRANT SELECT ON public.active_agencies TO authenticated, anon;

-- 6. Create a view for active agencies only
CREATE OR REPLACE VIEW public.active_agencies AS
SELECT 
    a.*,
    u.email,
    u.first_name,
    u.last_name,
    u.phone
FROM public.agencies a
JOIN public.users u ON a.user_id = u.id
WHERE u.role = 'agency' AND a.is_approved = true;

-- Grant access to the view
GRANT SELECT ON public.active_agencies TO authenticated, anon;
