-- FINAL FIX - Only fix available_cars view
-- Skip spatial_ref_sys as it's a PostGIS system table that doesn't need RLS

-- =============================================
-- FIX: available_cars view
-- =============================================

-- Drop the existing SECURITY DEFINER view
DROP VIEW IF EXISTS public.available_cars CASCADE;

-- Create a simple view without SECURITY DEFINER
CREATE VIEW public.available_cars AS
SELECT 
    c.id,
    c.brand,
    c.model,
    c.year,
    c.daily_rate,
    c.weekly_rate,
    c.monthly_rate,
    c.fuel_type,
    c.transmission,
    c.color,
    c.mileage,
    c.features,
    c.description,
    c.images,
    c.status,
    c.agency_id,
    c.address,
    c.seats,
    c.doors,
    c.license_plate,
    c.created_at,
    c.updated_at,
    a.agency_name,
    a.agency_logo,
    a.location as agency_location,
    a.agency_phone,
    a.rating as agency_rating
FROM public.cars c
INNER JOIN public.agencies a ON c.agency_id = a.id
WHERE c.status = 'available' AND a.is_approved = true;

-- Grant permissions to the view
GRANT SELECT ON public.available_cars TO anon, authenticated;

-- =============================================
-- VERIFICATION
-- =============================================

-- Check the view was created successfully
SELECT 
    schemaname, 
    viewname,
    'VIEW CREATED SUCCESSFULLY' as status
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'available_cars';

-- Test the view works
SELECT COUNT(*) as available_cars_count FROM public.available_cars;

-- Final status
SELECT 
    'available_cars view has been fixed' as message,
    'spatial_ref_sys is a PostGIS system table and does not need RLS' as note;
