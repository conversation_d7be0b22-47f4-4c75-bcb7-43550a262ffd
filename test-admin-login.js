// Simple test script to check admin login
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAdminLogin() {
    console.log('🧪 Testing admin login...')
    
    const adminEmail = '<EMAIL>'
    const adminPassword = 'Admin123!'
    
    try {
        // Try to sign in
        console.log('🔑 Attempting to sign in...')
        const { data, error } = await supabase.auth.signInWithPassword({
            email: adminEmail,
            password: adminPassword
        })
        
        if (error) {
            console.error('❌ Sign in failed:', error.message)
            return false
        }
        
        if (!data.user) {
            console.error('❌ No user data returned')
            return false
        }
        
        console.log('✅ Sign in successful!')
        console.log('User ID:', data.user.id)
        console.log('Email:', data.user.email)
        
        // Try to get user profile (this might fail due to RLS)
        console.log('👤 Fetching user profile...')
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()
            
        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            console.log('This might be due to RLS policies')
        } else {
            console.log('✅ Profile fetched successfully!')
            console.log('Role:', profile.role)
            console.log('Name:', profile.first_name, profile.last_name)
        }
        
        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')
        
        return true
    } catch (err) {
        console.error('❌ Unexpected error:', err.message)
        return false
    }
}

testAdminLogin().then(success => {
    if (success) {
        console.log('\n🎉 Admin login test completed!')
        console.log('You can try logging in with:')
        console.log('Email: <EMAIL>')
        console.log('Password: Admin123!')
    } else {
        console.log('\n❌ Admin login test failed!')
    }
}).catch(console.error)
